// Defines the base URL for the API from an environment variable
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001';
console.log('[API] Using API_BASE_URL:', API_BASE_URL);

// Import the Session type from Supabase
import { Session as SupabaseSession, User as SupabaseUser } from '@supabase/supabase-js';

// Use the same path as in AuthContext.tsx if it works there
import { PrismaUserProfile, LoginCredentials, RegisterData } from '@/types/user'; // CORRECTED IMPORT PATH & Added LoginCredentials, RegisterData
import { PaginatedEntities, GetEntitiesParams, Entity, EntityType, Category, Tag, Feature, PaginationMeta, CreateEntityDto } from '@/types/entity'; // Added import
import { Review, PaginatedReviews, CreateReviewPayload } from '@/types/review'; // Updated to PaginatedReviews
import { RecommendationResponse, RecommendationRequest } from '@/types/recommendation';
import { ChatRequest, ChatResponse, ChatMessage } from '@/types/chat';
import {
  CompleteProfileData,
  ProfileResponse,
  UserPreferences,
  PreferencesResponse,
  PreferencesUpdatePayload,
  ProfileUpdatePayload,
  ToolRequest,
  CreateToolRequestPayload,
  UserSubmittedTool
} from '@/types/profile';
import { convertEntityTypeFilters, validateEntityTypeFilters, sanitizeEntityTypeFilters } from '@/utils/entityTypeFilters';


export interface AuthResponse {
  message: string;
  user?: SupabaseUser; // Use SupabaseUser type
  session?: SupabaseSession; // Use SupabaseSession type
  error?: string;
  details?: Record<string, unknown>; // For validation errors or other details
}

/**
 * Logs in a user.
 * @param credentials - The user's email and password, as an object.
 * @returns A promise that resolves to the server's response.
 */
export const loginUser = async (credentials: LoginCredentials): Promise<AuthResponse> => {
  // console.log("[API] Logging in user with email:", credentials.email);
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, { 
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    const responseData = await response.json(); // Try to parse JSON regardless of status
    // console.log("[API] Login user response status:", response.status);

    if (!response.ok) {
      // console.error("[API] Login user error response data:", responseData);
      const errorMessage = responseData.message || `Login failed with status: ${response.status}`;
      console.error('[api.ts] Login API Error (v1.1):', responseData);
      const errorToThrow = new Error(errorMessage) as Error & { details?: unknown; statusCode?: number };
      errorToThrow.details = responseData;
      errorToThrow.statusCode = response.status;
      throw errorToThrow;
    }
    // Ensure the successful response matches AuthResponse structure, especially session and user
    return {
      message: responseData.message || "Login successful",
      user: responseData.user, 
      session: responseData.session,
    } as AuthResponse; 
  } catch (error) {
    if (error instanceof Error && 'statusCode' in error) {
        // This is an error we threw from the !response.ok block, re-throw it
        console.error('[api.ts] Re-throwing API error in loginUser (v1.1):', error); // DEBUG v1.1
        throw error;
    }
    console.error('[api.ts] Network or other error in loginUser (v1.1):', error); // DEBUG v1.1
    let message = "An unexpected network error occurred during login.";
    if (error instanceof Error) {
        message = error.message;
    }
    // For unexpected errors, throw a generic error object that form handlers can catch
    const genericError = new Error(message) as Error & { isNetworkError?: boolean };
    genericError.isNetworkError = true; // Flag it as a network/unexpected error
    throw genericError;
  }
};

/**
 * Registers a new user.
 * @param userData - The user's data for registration.
 * @returns A promise that resolves to the server's response.
 */
export const registerUser = async (userData: RegisterData): Promise<AuthResponse> => {
  // console.log("[API] Registering user with data:", {
  //   email: userData.email,
  //   password: userData.password ? '[PRESENT]' : '[ABSENT]',
  //   displayName: userData.displayName,
  // });
  try {
    const response = await fetch(`${API_BASE_URL}/auth/signup`, { // Ensure this endpoint matches your backend
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    const responseData = await response.json(); // Try to parse JSON regardless of status
    // console.log("[API] Register user response status:", response.status);

    if (!response.ok) {
      // console.error("[API] Register user error response data:", responseData);
      const errorMessage = responseData.message || `Registration failed with status: ${response.status}`;
      console.error('[api.ts] Registration API Error:', responseData);
      const errorToThrow = new Error(errorMessage) as Error & { details?: unknown; statusCode?: number };
      errorToThrow.details = responseData;
      errorToThrow.statusCode = response.status;
      throw errorToThrow;
    }
    // Ensure the successful response matches AuthResponse structure
    return {
      message: responseData.message || "Registration successful",
      user: responseData.user, // May not be returned on signup, depending on backend
      session: responseData.session, // May not be returned on signup, depending on backend
    } as AuthResponse;
  } catch (error) {
    if (error instanceof Error && 'statusCode' in error) {
        // This is an error we threw from the !response.ok block, re-throw it
        console.error('[api.ts] Re-throwing API error in registerUser:', error);
        throw error;
    }
    console.error('[api.ts] Network or other error in registerUser:', error); // DEBUG
    let message = "An unexpected network error occurred during registration.";
    if (error instanceof Error) {
        message = error.message;
    }
    const genericError = new Error(message) as Error & { isNetworkError?: boolean };
    genericError.isNetworkError = true;
    throw genericError;
  }
};

// syncUserProfileAPI modified to accept session directly
export const syncUserProfileAPI = async (session: SupabaseSession | null): Promise<PrismaUserProfile> => {
  if (!session || !session.access_token) {
    throw new Error("No session or access token provided for profile sync.");
  }

  console.log('[API] syncUserProfileAPI: Attempting to sync profile with:', `${API_BASE_URL}/auth/sync-profile`);

  try {
    const response = await fetch(`${API_BASE_URL}/auth/sync-profile`, { // Corrected Endpoint
      method: 'POST', // Corrected Method
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
      },
      // body: JSON.stringify({}), // Backend might not require a body if JWT is sufficient
    });

    console.log('[API] syncUserProfileAPI: Response status:', response.status);

    if (!response.ok) {
      const errorBody = await response.text();
      console.error('[API] syncUserProfileAPI: Error response body:', errorBody);
      throw new Error(`Failed to sync user profile. Status: ${response.status}. Body: ${errorBody}`);
    }

    const data: PrismaUserProfile = await response.json();
    console.log('[API] syncUserProfileAPI: Successfully synced profile:', data);
    return data;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error("[API] Error in syncUserProfileAPI:", errorMessage);
    throw error;
  }
};

/**
 * Fetches a paginated list of entities from the backend.
 * @param params - Query parameters for filtering, sorting, and pagination.
 * @param token - Optional access token for authenticated requests (if needed for entities).
 * @returns A promise that resolves to the paginated list of entities.
 */
export const getEntities = async (
  params?: GetEntitiesParams,
  token?: string | null,
): Promise<PaginatedEntities> => {
  const queryParams = new URLSearchParams();

  // Helper function to ensure array parameters are properly handled
  const addArrayParam = (paramName: string, paramValue: string[] | string | undefined) => {
    if (!paramValue) return;

    // Convert to array if it's not already
    let arrayValue: string[];
    if (Array.isArray(paramValue)) {
      arrayValue = paramValue;
    } else if (typeof paramValue === 'string') {
      arrayValue = [paramValue];
    } else {
      return;
    }

    // Filter out empty values and add to query
    const validValues = arrayValue.filter(value => value && typeof value === 'string' && value.trim());
    if (validValues.length > 0) {
      validValues.forEach((value) => queryParams.append(paramName, value));
    }
  };

  if (params) {
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.searchTerm) queryParams.append('searchTerm', params.searchTerm);
    addArrayParam('categoryIds', params.categoryIds);
    addArrayParam('tagIds', params.tagIds);
    // Backend expects entityTypeIds with UUIDs, not entity_types with slugs
    addArrayParam('entityTypeIds', params.entityTypeIds);
    addArrayParam('featureIds', params.featureIds);
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    if (params.status) queryParams.append('status', params.status);
    if (params.submitterId) queryParams.append('submitterId', params.submitterId);
    if (params.entityTypeId) queryParams.append('entityTypeId', params.entityTypeId);

    // Date filters
    if (params.createdAtFrom) queryParams.append('createdAtFrom', params.createdAtFrom);
    if (params.createdAtTo) queryParams.append('createdAtTo', params.createdAtTo);

    // Boolean filters
    if (params.hasFreeTier !== undefined) queryParams.append('hasFreeTier', params.hasFreeTier.toString());
    if (params.apiAccess !== undefined) queryParams.append('apiAccess', params.apiAccess.toString());

    // Array filters - use helper function for robust handling
    addArrayParam('employeeCountRanges', params.employeeCountRanges);
    addArrayParam('fundingStages', params.fundingStages);
    addArrayParam('pricingModels', params.pricingModels);
    addArrayParam('priceRanges', params.priceRanges);
    addArrayParam('integrations', params.integrations);
    addArrayParam('platforms', params.platforms);
    addArrayParam('targetAudience', params.targetAudience);

    // Location search
    if (params.locationSearch) queryParams.append('locationSearch', params.locationSearch);

    // Rating filters
    if (params.rating_min !== undefined) queryParams.append('rating_min', params.rating_min.toString());
    if (params.rating_max !== undefined) queryParams.append('rating_max', params.rating_max.toString());

    // Review count filters
    if (params.review_count_min !== undefined) queryParams.append('review_count_min', params.review_count_min.toString());
    if (params.review_count_max !== undefined) queryParams.append('review_count_max', params.review_count_max.toString());

    // Affiliate filters
    if (params.affiliate_status) queryParams.append('affiliate_status', params.affiliate_status);
    if (params.has_affiliate_link !== undefined) queryParams.append('has_affiliate_link', params.has_affiliate_link.toString());

    // ✅ ENTITY-SPECIFIC FILTERS - NOW WORKING WITH FLAT PARAMETERS! 🎉

    // Tool/AI Tool filters
    addArrayParam('technical_levels', params.technical_levels);
    addArrayParam('learning_curves', params.learning_curves);
    addArrayParam('pricing_models', params.pricingModels);
    addArrayParam('price_ranges', params.priceRanges);
    if (params.has_api !== undefined) queryParams.append('has_api', params.has_api.toString());
    if (params.has_free_tier !== undefined) queryParams.append('has_free_tier', params.has_free_tier.toString());
    if (params.open_source !== undefined) queryParams.append('open_source', params.open_source.toString());
    if (params.mobile_support !== undefined) queryParams.append('mobile_support', params.mobile_support.toString());
    if (params.demo_available !== undefined) queryParams.append('demo_available', params.demo_available.toString());
    if (params.has_live_chat !== undefined) queryParams.append('has_live_chat', params.has_live_chat.toString());
    addArrayParam('frameworks', params.frameworks);
    addArrayParam('libraries', params.libraries);
    addArrayParam('deployment_options', params.deployment_options);
    addArrayParam('support_channels', params.support_channels);
    if (params.key_features_search) queryParams.append('key_features_search', params.key_features_search);
    if (params.use_cases_search) queryParams.append('use_cases_search', params.use_cases_search);
    if (params.target_audience_search) queryParams.append('target_audience_search', params.target_audience_search);
    if (params.customization_level) queryParams.append('customization_level', params.customization_level);
    if (params.pricing_details_search) queryParams.append('pricing_details_search', params.pricing_details_search);

    // Course filters
    addArrayParam('skill_levels', params.skill_levels);
    if (params.certificate_available !== undefined) queryParams.append('certificate_available', params.certificate_available.toString());
    if (params.instructor_name) queryParams.append('instructor_name', params.instructor_name);
    if (params.duration_text) queryParams.append('duration_text', params.duration_text);
    if (params.enrollment_min !== undefined) queryParams.append('enrollment_min', params.enrollment_min.toString());
    if (params.enrollment_max !== undefined) queryParams.append('enrollment_max', params.enrollment_max.toString());
    if (params.prerequisites) queryParams.append('prerequisites', params.prerequisites);
    if (params.has_syllabus !== undefined) queryParams.append('has_syllabus', params.has_syllabus.toString());

    // Job filters
    addArrayParam('employment_types', params.employment_types);
    addArrayParam('experience_levels', params.experience_levels);
    addArrayParam('location_types', params.location_types);
    if (params.company_name) queryParams.append('company_name', params.company_name);
    if (params.job_title) queryParams.append('job_title', params.job_title);
    if (params.salary_min !== undefined) queryParams.append('salary_min', params.salary_min.toString());
    if (params.salary_max !== undefined) queryParams.append('salary_max', params.salary_max.toString());
    if (params.has_application_url !== undefined) queryParams.append('has_application_url', params.has_application_url.toString());

    // Hardware filters
    addArrayParam('hardware_types', params.hardware_types);
    addArrayParam('manufacturers', params.manufacturers);
    if (params.price_range) queryParams.append('price_range', params.price_range);
    if (params.price_min !== undefined) queryParams.append('price_min', params.price_min.toString());
    if (params.price_max !== undefined) queryParams.append('price_max', params.price_max.toString());
    if (params.memory_search) queryParams.append('memory_search', params.memory_search);
    if (params.processor_search) queryParams.append('processor_search', params.processor_search);
    if (params.release_date_from) queryParams.append('release_date_from', params.release_date_from);
    if (params.release_date_to) queryParams.append('release_date_to', params.release_date_to);
    if (params.has_datasheet !== undefined) queryParams.append('has_datasheet', params.has_datasheet.toString());

    // Event filters
    addArrayParam('event_types', params.event_types);
    if (params.start_date_from) queryParams.append('start_date_from', params.start_date_from);
    if (params.start_date_to) queryParams.append('start_date_to', params.start_date_to);
    if (params.end_date_from) queryParams.append('end_date_from', params.end_date_from);
    if (params.end_date_to) queryParams.append('end_date_to', params.end_date_to);
    if (params.is_online !== undefined) queryParams.append('is_online', params.is_online.toString());
    if (params.location) queryParams.append('location', params.location);
    if (params.price_text) queryParams.append('price_text', params.price_text);
    if (params.has_registration_url !== undefined) queryParams.append('has_registration_url', params.has_registration_url.toString());
    if (params.speakers_search) queryParams.append('speakers_search', params.speakers_search);

    // Agency filters
    addArrayParam('services_offered', params.services_offered);
    addArrayParam('industry_focus', params.industry_focus);
    if (params.has_portfolio !== undefined) queryParams.append('has_portfolio', params.has_portfolio.toString());

    // Software filters
    addArrayParam('license_types', params.license_types);
    addArrayParam('programming_languages', params.programming_languages);
    addArrayParam('platform_compatibility', params.platform_compatibility);
    if (params.current_version) queryParams.append('current_version', params.current_version);
    if (params.has_repository !== undefined) queryParams.append('has_repository', params.has_repository.toString());

    // Research Paper filters
    addArrayParam('research_areas', params.research_areas);
    if (params.authors_search) queryParams.append('authors_search', params.authors_search);
    if (params.publication_date_from) queryParams.append('publication_date_from', params.publication_date_from);
    if (params.publication_date_to) queryParams.append('publication_date_to', params.publication_date_to);

    // Book filters
    if (params.author_name) queryParams.append('author_name', params.author_name);
    if (params.isbn) queryParams.append('isbn', params.isbn);
    addArrayParam('formats', params.formats);

    // Platform & Integration filters (existing)
    addArrayParam('integrations', params.integrations);
    addArrayParam('platforms', params.platforms);
    addArrayParam('targetAudience', params.targetAudience);

    // Status & Moderation filters
    if (params.status) queryParams.append('status', params.status);
    if (params.submitterId) queryParams.append('submitterId', params.submitterId);
  }

  const fullUrl = `${API_BASE_URL}/entities?${queryParams.toString()}`;
  // console.log("[API] Fetching entities from:", fullUrl); // Optional: Log the URL

  try {
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        // 'Content-Type': 'application/json', // Removed for GET
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      let errorData: Record<string, unknown> = {};
      let responseText = '';
      try {
        responseText = await response.text();
        if (responseText) { // Attempt to parse only if responseText is not empty
          errorData = JSON.parse(responseText);
        }
      } catch {
        // JSON parsing failed or responseText was initially empty.
        // errorData will remain {} or be partially filled if parse started but failed.
        // console.error('[API] getEntities: Failed to parse error response as JSON. Raw text:', responseText, 'Error:', e);
      }

      const status = response.status;
      const messageFromServer = errorData?.message;
      let errorMessage: string;

      if (typeof messageFromServer === 'string' && messageFromServer.trim() !== "") {
        errorMessage = messageFromServer;
      } else {
        errorMessage = `Request failed with status ${status}.`;
        // If the server message wasn't usable, and we have responseText, it's likely more informative.
        if (responseText) {
          errorMessage += ` Response: ${responseText.substring(0, 100)}${responseText.length > 100 ? '...' : ''}`;
        } else {
          // Server message not usable, no responseText either.
          errorMessage += ` The response body was empty or not valid text.`;
        }
      }
      
      console.error(`[API Error getEntities] Status: ${status}. Message: ${errorMessage}. Details:`, errorData, `Raw Response: ${responseText.substring(0,200)}`);

      const errorToThrow = new Error(errorMessage) as Error & {
        statusCode?: number;
        errorDetails?: Record<string, unknown>;
        responseText?: string;
      };
      errorToThrow.statusCode = status;
      errorToThrow.errorDetails = errorData; // Store full parsed error data
      errorToThrow.responseText = responseText; // Store full raw response text
      throw errorToThrow;
    }

    const result = await response.json();

    // Transform the response to match PaginatedEntities interface
    if (result && result.total !== undefined && result.page !== undefined && result.totalPages !== undefined) {
      const transformedResult = {
        data: result.data,
        meta: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          totalItems: result.total,
          totalPages: result.totalPages,
          hasNextPage: result.page < result.totalPages,
          hasPrevPage: result.page > 1,
        }
      };
      return transformedResult;
    }

    return result;
  } catch (error: unknown) {
    // If the error object has a statusCode property, it means we processed it from a non-ok response
    if (error && typeof error === 'object' && 'statusCode' in error && typeof (error as { statusCode: unknown }).statusCode === 'number') {
      // console.error('[API Catch getEntities] Re-throwing processed API error:', error.message);
      throw error; // Re-throw the original error object with all its details
    }
    // For other types of errors (e.g., network issues, or errors thrown before response.ok is checked)
    const originalMessage = error instanceof Error ? error.message : String(error);
    console.error('[API Catch getEntities] Network or other error, wrapping:', originalMessage);
    throw new Error(`Network error or unexpected issue fetching entities: ${originalMessage}`);
  }
};

/**
 * Fetches a single entity by its ID from the backend.
 * @param id - The ID of the entity to fetch.
 * @param token - Optional access token for authenticated requests.
 * @returns A promise that resolves to the entity.
 */
export const getEntityById = async (
  id: string,
  token?: string | null,
): Promise<Entity> => {
  const response = await fetch(`${API_BASE_URL}/entities/${id}`, {
    method: 'GET',
    headers: {
      // 'Content-Type': 'application/json', // Removed for GET
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch entity and parse error response' }));
    console.error(`Error fetching entity ${id}:`, errorData);
    throw new Error(
      errorData.message || `Failed to fetch entity ${id}. Status: ${response.status}`,
    );
  }
  return response.json();
};

/**
 * Fetches a single entity by its slug from the backend.
 * @param slug - The slug of the entity to fetch.
 * @param token - Optional access token for authenticated requests.
 * @returns A promise that resolves to the entity.
 */
export const getEntityBySlug = async (
  slug: string,
  token?: string | null,
): Promise<Entity> => {
  const response = await fetch(`${API_BASE_URL}/entities/slug/${slug}`, {
    method: 'GET',
    headers: {
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch entity and parse error response' }));
    console.error(`Error fetching entity with slug ${slug}:`, errorData);
    throw new Error(
      errorData.message || `Failed to fetch entity with slug ${slug}. Status: ${response.status}`,
    );
  }
  return response.json();
};

/**
 * Fetches reviews for a specific entity by its ID from the backend.
 * @param entityId - The ID of the entity for which to fetch reviews.
 * @param token - Optional access token for authenticated requests.
 * @param page - Optional page number for pagination.
 * @param limit - Optional limit for pagination.
 * @returns A promise that resolves to a list of reviews.
 */
export const getReviewsByEntityId = async (
  entityId: string,
  token?: string | null,
  page?: number,
  limit?: number,
): Promise<PaginatedReviews> => { 
  const queryParams = new URLSearchParams();
  if (page) queryParams.append('page', page.toString());
  if (limit) queryParams.append('limit', limit.toString());
  // Add other query params if your backend supports them (e.g., sortBy, sortOrder for reviews)

  const response = await fetch(`${API_BASE_URL}/entities/${entityId}/reviews?${queryParams.toString()}`, {
    method: 'GET',
    headers: {
      // 'Content-Type': 'application/json', // Removed for GET
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch reviews and parse error response' }));
    console.error(`Error fetching reviews for entity ${entityId}:`, errorData);
    throw new Error(
      errorData.message || `Failed to fetch reviews for entity ${entityId}. Status: ${response.status}`,
    );
  }
  return response.json(); 
};

// Function to submit a new review
export const submitReview = async (
  payload: CreateReviewPayload,
  token: string, // Assuming review submission requires authentication
): Promise<Review> => { // Assuming the backend returns the created review
  const response = await fetch(`${API_BASE_URL}/entities/${payload.entityId}/reviews`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({
      rating: payload.rating,
      title: payload.title,
      reviewText: payload.text,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to submit review and parse error response' }));
    console.error('Error submitting review:', errorData);

    // Handle validation errors more gracefully
    if (response.status === 400 && errorData.message) {
      // If it's a validation error with multiple issues, format them nicely
      if (Array.isArray(errorData.message)) {
        const formattedErrors = errorData.message.join(', ');
        throw new Error(`Please fix the following issues: ${formattedErrors}`);
      } else if (typeof errorData.message === 'string') {
        // Clean up common validation error patterns
        const cleanedMessage = errorData.message
          .replace(/property \w+ should not exist,?/g, '')
          .replace(/,+/g, ', ')
          .replace(/^,\s*/, '')
          .replace(/,\s*$/, '');
        throw new Error(cleanedMessage || 'Please check your review details and try again.');
      }
    }

    throw new Error(
      errorData.message || `Failed to submit review. Status: ${response.status}`,
    );
  }
  return response.json();
};

/**
 * Fetches all available entity types from the backend.
 * @param token - Optional access token for authenticated requests.
 * @returns A promise that resolves to a list of entity types.
 */
export const getEntityTypes = async (token?: string | null): Promise<EntityType[]> => {
  const response = await fetch(`${API_BASE_URL}/entity-types`, { // Assuming endpoint is /entity-types
    method: 'GET',
    headers: {
      // 'Content-Type': 'application/json', // Removed for GET
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch entity types and parse error response' }));
    console.error('Error fetching entity types:', errorData);
    throw new Error(
      errorData.message || `Failed to fetch entity types. Status: ${response.status}`,
    );
  }
  return response.json(); 
};

/**
 * Fetches all public categories from the backend.
 * @param token - Optional access token (if endpoint becomes protected).
 * @returns A promise that resolves to a list of categories.
 */
export const getCategories = async (token?: string | null): Promise<Category[]> => {
  const response = await fetch(`${API_BASE_URL}/categories`, { // Assuming public endpoint /categories
    method: 'GET',
    headers: {
      // 'Content-Type': 'application/json', // Removed for GET
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch categories and parse error response' }));
    console.error('Error fetching categories:', errorData);
    throw new Error(
      errorData.message || `Failed to fetch categories. Status: ${response.status}`,
    );
  }
  return response.json(); 
};

/**
 * Fetches all public tags from the backend.
 * @param token - Optional access token (if endpoint becomes protected).
 * @returns A promise that resolves to a list of tags.
 */
export const getTags = async (token?: string | null): Promise<Tag[]> => {
  const response = await fetch(`${API_BASE_URL}/tags`, { // Assuming public endpoint /tags
    method: 'GET',
    headers: {
      // 'Content-Type': 'application/json', // Removed for GET
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch tags and parse error response' }));
    console.error('Error fetching tags:', errorData);
    throw new Error(
      errorData.message || `Failed to fetch tags. Status: ${response.status}`,
    );
  }
  return response.json();
};

/**
 * Fetches all public features from the backend.
 * @param token - Optional access token (if endpoint becomes protected).
 * @returns A promise that resolves to a list of features.
 */
export const getFeatures = async (token?: string | null): Promise<Feature[]> => {
  const response = await fetch(`${API_BASE_URL}/features`, { // Assuming public endpoint /features
    method: 'GET',
    headers: {
      // 'Content-Type': 'application/json', // Removed for GET
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch features and parse error response' }));
    console.error('Error fetching features:', errorData);
    throw new Error(
      errorData.message || `Failed to fetch features. Status: ${response.status}`,
    );
  }
  return response.json();
};

/**
 * Bookmarks an entity for the current user.
 * @param entityId - The ID of the entity to bookmark.
 * @param token - Access token for authenticated requests.
 * @returns A promise that resolves when the bookmark is created.
 */
export const bookmarkEntity = async (
  entityId: string,
  token: string,
): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/bookmarks`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({ entity_id: entityId }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to bookmark entity and parse error response' }));
    console.error(`Error bookmarking entity ${entityId}:`, errorData);
    throw new Error(
      errorData.message || `Failed to bookmark entity. Status: ${response.status}`,
    );
  }
};

/**
 * Removes a bookmark for an entity for the current user.
 * @param entityId - The ID of the entity to unbookmark.
 * @param token - Access token for authenticated requests.
 * @returns A promise that resolves when the bookmark is removed.
 */
export const unbookmarkEntity = async (
  entityId: string,
  token: string,
): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/bookmarks/${entityId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to unbookmark entity and parse error response' }));
    console.error(`Error unbookmarking entity ${entityId}:`, errorData);
    throw new Error(
      errorData.message || `Failed to unbookmark entity. Status: ${response.status}`,
    );
  }
};

/**
 * Gets the list of bookmarked entities for the current user.
 * @param token - Access token for authenticated requests.
 * @param page - Optional page number for pagination.
 * @param limit - Optional limit for pagination.
 * @returns A promise that resolves to a list of bookmarked entities.
 */
export const getBookmarkedEntities = async (
  token: string,
  page?: number,
  limit?: number,
): Promise<{ data: Entity[]; meta: PaginationMeta }> => {
  const queryParams = new URLSearchParams();
  if (page) queryParams.append('page', page.toString());
  if (limit) queryParams.append('limit', limit.toString());

  const response = await fetch(`${API_BASE_URL}/bookmarks?${queryParams.toString()}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch bookmarks and parse error response' }));
    console.error('Error fetching bookmarks:', errorData);
    throw new Error(
      errorData.message || `Failed to fetch bookmarks. Status: ${response.status}`,
    );
  }

  const result = await response.json();
  console.log('[API] getBookmarkedEntities: Raw response:', result);

  // Handle ApiResponseDto format: { success: boolean, data: T, meta?: PaginationMeta, message?: string }
  if (result.success !== undefined && result.data !== undefined) {
    return {
      data: Array.isArray(result.data) ? result.data : [],
      meta: result.meta || {
        total: Array.isArray(result.data) ? result.data.length : 0,
        page: page || 1,
        limit: limit || 20,
        totalItems: Array.isArray(result.data) ? result.data.length : 0,
        totalPages: 1,
        hasNextPage: false,
        hasPrevPage: false,
      }
    };
  }

  // If backend returns the expected format directly
  if (result.data && result.meta) {
    return result;
  }

  // Fallback for unexpected format
  const dataArray = Array.isArray(result) ? result : [];
  const totalItems = dataArray.length;
  const currentPage = page || 1;
  const pageLimit = limit || 20;
  const totalPages = Math.ceil(totalItems / pageLimit);

  return {
    data: dataArray,
    meta: {
      total: totalItems,
      page: currentPage,
      limit: pageLimit,
      totalItems: totalItems,
      totalPages: totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    }
  };
};

/**
 * Gets AI-powered recommendations based on user's problem description
 * @param payload - The recommendation request payload containing problem description and optional filters
 * @param token - Optional access token for authenticated requests
 * @returns A promise that resolves to the recommendation response
 */
export const getRecommendations = async (
  payload: RecommendationRequest,
  token?: string | null,
): Promise<RecommendationResponse> => {
  const response = await fetch(`${API_BASE_URL}/recommendations`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({
      message: 'Failed to get recommendations and parse error response'
    }));
    console.error('Error getting recommendations:', errorData);
    throw new Error(
      errorData.message || `Failed to get recommendations. Status: ${response.status}`,
    );
  }

  return response.json();
};

// ===== UPVOTE API FUNCTIONS =====

/**
 * Adds an upvote to an entity
 * @param entityId - The ID of the entity to upvote
 * @param token - JWT access token for authentication
 * @returns A promise that resolves when the upvote is added
 */
export const addUpvote = async (entityId: string, token: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/entities/${entityId}/upvote`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    let errorData: any = {};
    let responseText = '';

    try {
      // First try to get the response as text
      responseText = await response.text();
      console.log(`[API] Raw error response for entity ${entityId}:`, responseText);

      // Then try to parse as JSON
      if (responseText) {
        errorData = JSON.parse(responseText);
      }
    } catch (parseError) {
      console.log(`[API] Failed to parse error response as JSON:`, parseError);
      errorData = {
        message: responseText || `HTTP ${response.status}: ${response.statusText}`,
        rawResponse: responseText
      };
    }

    console.error(`Error adding upvote for entity ${entityId}:`, {
      status: response.status,
      statusText: response.statusText,
      url: response.url,
      errorData,
      responseText
    });

    // Provide more specific error messages based on status code
    let errorMessage = errorData.message || errorData.error || `Failed to add upvote. Status: ${response.status}`;

    if (response.status === 400) {
      errorMessage = `Bad Request: ${errorData.message || errorData.error || 'Invalid entity ID or request format'}`;
    } else if (response.status === 404) {
      errorMessage = `Entity not found: ${entityId}`;
    } else if (response.status === 401) {
      errorMessage = 'Authentication required to upvote entities';
    } else if (response.status === 403) {
      errorMessage = 'Permission denied to upvote this entity';
    } else if (response.status >= 500) {
      errorMessage = `Server error: ${errorData.message || 'Internal server error'}`;
    }

    throw new Error(errorMessage);
  }
  // Note: API returns 201 for new upvotes, 200 for existing (idempotent)
};

/**
 * Removes an upvote from an entity
 * @param entityId - The ID of the entity to remove upvote from
 * @param token - JWT access token for authentication
 * @returns A promise that resolves when the upvote is removed
 */
export const removeUpvote = async (entityId: string, token: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/entities/${entityId}/upvote`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    let errorData: any = {};
    let responseText = '';

    try {
      // First try to get the response as text
      responseText = await response.text();
      console.log(`[API] Raw error response for removing upvote ${entityId}:`, responseText);

      // Then try to parse as JSON
      if (responseText) {
        errorData = JSON.parse(responseText);
      }
    } catch (parseError) {
      console.log(`[API] Failed to parse error response as JSON:`, parseError);
      errorData = {
        message: responseText || `HTTP ${response.status}: ${response.statusText}`,
        rawResponse: responseText
      };
    }

    console.error(`Error removing upvote for entity ${entityId}:`, {
      status: response.status,
      statusText: response.statusText,
      url: response.url,
      errorData,
      responseText
    });

    // Provide more specific error messages based on status code
    let errorMessage = errorData.message || errorData.error || `Failed to remove upvote. Status: ${response.status}`;

    if (response.status === 400) {
      errorMessage = `Bad Request: ${errorData.message || errorData.error || 'Invalid entity ID or request format'}`;
    } else if (response.status === 404) {
      errorMessage = `Entity not found: ${entityId}`;
    } else if (response.status === 401) {
      errorMessage = 'Authentication required to remove upvote';
    } else if (response.status === 403) {
      errorMessage = 'Permission denied to remove upvote from this entity';
    } else if (response.status >= 500) {
      errorMessage = `Server error: ${errorData.message || 'Internal server error'}`;
    }

    throw new Error(errorMessage);
  }
  // Note: API returns 204 No Content for successful removal
};

/**
 * Fetches the list of entity IDs that the current user has upvoted
 * @param token - JWT access token for authentication
 * @returns A promise that resolves to an array of entity IDs
 */
export const getUpvotedEntityIds = async (token: string): Promise<string[]> => {
  const response = await fetch(`${API_BASE_URL}/users/me/upvoted-ids`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch upvoted entity IDs and parse error response' }));
    console.error('Error fetching upvoted entity IDs:', errorData);

    // Handle 401 specifically for auth errors
    if (response.status === 401) {
      throw new Error('Authentication required to fetch upvoted entities');
    }

    throw new Error(
      errorData.message || `Failed to fetch upvoted entity IDs. Status: ${response.status}`,
    );
  }

  const upvotedIds = await response.json();

  // Validate response is an array
  if (!Array.isArray(upvotedIds)) {
    console.error('Invalid response format for upvoted entity IDs:', upvotedIds);
    throw new Error('Invalid response format from server');
  }

  return upvotedIds;
};

/**
 * Gets the complete profile data for the current user.
 * @param token - Access token for authenticated requests.
 * @returns A promise that resolves to the complete profile data.
 */
export const getCompleteProfile = async (token: string): Promise<CompleteProfileData> => {
  console.log('[API] getCompleteProfile: Fetching complete profile from:', `${API_BASE_URL}/users/me/profile`);
  const response = await fetch(`${API_BASE_URL}/users/me/profile`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  console.log('[API] getCompleteProfile: Response status:', response.status);
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch profile and parse error response' }));
    console.error('[API] getCompleteProfile: Error fetching complete profile:', errorData);
    throw new Error(
      errorData.message || `Failed to fetch profile. Status: ${response.status}`,
    );
  }

  const result: ProfileResponse = await response.json();
  console.log('[API] getCompleteProfile: Successfully fetched profile:', result);
  return result.data;
};

/**
 * Updates the user's profile information.
 * @param payload - The profile update data.
 * @param token - Access token for authenticated requests.
 * @returns A promise that resolves to the updated user profile.
 */
export const updateUserProfile = async (
  payload: ProfileUpdatePayload,
  token: string,
): Promise<PrismaUserProfile> => {
  console.log('[API] Updating user profile with payload:', JSON.stringify(payload, null, 2));
  const response = await fetch(`${API_BASE_URL}/users/me`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(payload),
  });

  console.log('[API] Profile update response status:', response.status);
  if (!response.ok) {
    let errorData;
    try {
      errorData = await response.json();
    } catch (parseError) {
      console.error('[API] Failed to parse error response:', parseError);
      errorData = { message: `Failed to update profile. Status: ${response.status}` };
    }

    console.error('[API] Error updating profile:', errorData);
    console.error('[API] Full error response:', { status: response.status, statusText: response.statusText, errorData });

    // Handle different error response formats
    if (errorData.message) {
      throw new Error(errorData.message);
    } else if (errorData.error) {
      throw new Error(errorData.error);
    } else if (typeof errorData === 'string') {
      throw new Error(errorData);
    } else {
      throw new Error(`Failed to update profile. Status: ${response.status}`);
    }
  }

  const result = await response.json();
  console.log('[API] Profile update response:', result);

  // Handle ApiResponseDto format: { success: boolean, data: T, message?: string }
  if (result.success !== undefined && result.data !== undefined) {
    return result.data;
  }

  // If the response is the user object directly
  if (result.id && result.email) {
    return result;
  }

  // Fallback - return the result as-is
  return result;
};

/**
 * Gets the user's preferences.
 * @param token - Access token for authenticated requests.
 * @returns A promise that resolves to the user preferences.
 */
export const getUserPreferences = async (token: string): Promise<UserPreferences> => {
  console.log('[API] Fetching user preferences from:', `${API_BASE_URL}/users/me/preferences`);
  const response = await fetch(`${API_BASE_URL}/users/me/preferences`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  console.log('[API] Preferences response status:', response.status);
  if (!response.ok) {
    // Handle 404 as "preferences not found" - this is expected for new users
    if (response.status === 404) {
      console.log('[API] Preferences not found (404) - user may not have preferences yet');
      throw new Error('Preferences not found');
    }

    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch preferences and parse error response' }));
    console.error('[API] Error fetching preferences:', errorData);
    throw new Error(
      errorData.message || `Failed to fetch preferences. Status: ${response.status}`,
    );
  }

  const result = await response.json();
  console.log('[API] Preferences response:', result);

  // Handle ApiResponseDto format: { success: boolean, data: T, message?: string }
  if (result.success !== undefined && result.data !== undefined) {
    return result.data;
  }

  // Handle PreferencesResponse format: { success: boolean, data: UserPreferences, message?: string }
  if (result.data) {
    return result.data;
  }

  // If the response is the preferences object directly
  if (result.id && result.user_id) {
    return result;
  }

  // If no valid preferences found, throw an error
  throw new Error('Preferences not found');
};

/**
 * Updates the user's preferences.
 * @param payload - The preferences update data.
 * @param token - Access token for authenticated requests.
 * @returns A promise that resolves to the updated preferences.
 */
export const updateUserPreferences = async (
  payload: PreferencesUpdatePayload,
  token: string,
): Promise<UserPreferences> => {
  const response = await fetch(`${API_BASE_URL}/users/me/preferences`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to update preferences and parse error response' }));
    console.error('Error updating preferences:', errorData);
    throw new Error(
      errorData.message || `Failed to update preferences. Status: ${response.status}`,
    );
  }

  const result: PreferencesResponse = await response.json();
  return result.data;
};

/**
 * Gets the user's tool requests.
 * @param token - Access token for authenticated requests.
 * @param page - Optional page number for pagination.
 * @param limit - Optional limit for pagination.
 * @returns A promise that resolves to the user's tool requests.
 */
export const getUserToolRequests = async (
  token: string
): Promise<{ data: ToolRequest[]; meta: { total: number; page: number; limit: number; total_pages: number } }> => {
  // Backend doesn't accept pagination parameters, so we'll call without them
  console.log('[API] getUserToolRequests: Backend does not accept page/limit parameters, calling without them');

  const response = await fetch(`${API_BASE_URL}/users/me/tool-requests`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch tool requests and parse error response' }));
    console.error('Error fetching tool requests:', errorData);
    throw new Error(
      errorData.message || `Failed to fetch tool requests. Status: ${response.status}`,
    );
  }

  const result = await response.json();
  console.log('[API] getUserToolRequests: Raw response:', result);

  // Handle ApiResponseDto format: { success: boolean, data: T, message?: string }
  if (result.success !== undefined && result.data !== undefined) {
    const data = Array.isArray(result.data) ? result.data : [];
    return {
      data,
      meta: {
        total: data.length,
        page: 1,
        limit: data.length,
        total_pages: 1,
      }
    };
  }

  // If backend returns array directly, wrap it in expected format
  if (Array.isArray(result)) {
    return {
      data: result,
      meta: {
        total: result.length,
        page: 1,
        limit: result.length,
        total_pages: 1,
      }
    };
  }

  // If backend returns object with data property, use it
  if (result.data) {
    const data = Array.isArray(result.data) ? result.data : [];
    return {
      data,
      meta: result.meta || {
        total: data.length,
        page: 1,
        limit: data.length,
        total_pages: 1
      }
    };
  }

  // Fallback
  return { data: [], meta: { total: 0, page: 1, limit: 0, total_pages: 0 } };
};

/**
 * Creates a new tool request.
 * @param payload - The tool request data.
 * @param token - Access token for authenticated requests.
 * @returns A promise that resolves to the created tool request.
 */
export const createToolRequest = async (
  payload: CreateToolRequestPayload,
  token: string,
): Promise<ToolRequest> => {
  const response = await fetch(`${API_BASE_URL}/tool-requests`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to create tool request and parse error response' }));
    console.error('Error creating tool request:', errorData);
    throw new Error(
      errorData.message || `Failed to create tool request. Status: ${response.status}`,
    );
  }

  return response.json();
};

/**
 * Gets the user's submitted tools.
 * @param token - Access token for authenticated requests.
 * @param page - Optional page number for pagination.
 * @param limit - Optional limit for pagination.
 * @returns A promise that resolves to the user's submitted tools.
 */
export const getUserSubmittedTools = async (
  token: string
): Promise<{ data: UserSubmittedTool[]; meta: { total: number; page: number; limit: number; total_pages: number } }> => {
  // Backend doesn't accept pagination parameters, so we'll call without them
  console.log('[API] getUserSubmittedTools: Backend does not accept page/limit parameters, calling without them');

  const response = await fetch(`${API_BASE_URL}/users/me/submitted-tools`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch submitted tools and parse error response' }));
    console.error('Error fetching submitted tools:', errorData);
    throw new Error(
      errorData.message || `Failed to fetch submitted tools. Status: ${response.status}`,
    );
  }

  const result = await response.json();
  console.log('[API] getUserSubmittedTools: Raw response:', result);

  // Handle ApiResponseDto format: { success: boolean, data: T, message?: string }
  if (result.success !== undefined && result.data !== undefined) {
    const data = Array.isArray(result.data) ? result.data : [];
    return {
      data,
      meta: {
        total: data.length,
        page: 1,
        limit: data.length,
        total_pages: 1,
      }
    };
  }

  // If backend returns array directly, wrap it in expected format
  if (Array.isArray(result)) {
    return {
      data: result,
      meta: {
        total: result.length,
        page: 1,
        limit: result.length,
        total_pages: 1,
      }
    };
  }

  // If backend returns object with data property, use it
  if (result.data) {
    const data = Array.isArray(result.data) ? result.data : [];
    return {
      data,
      meta: result.meta || {
        total: data.length,
        page: 1,
        limit: data.length,
        total_pages: 1
      }
    };
  }

  // Fallback
  return { data: [], meta: { total: 0, page: 1, limit: 0, total_pages: 0 } };
};

/**
 * Creates a new entity by submitting it to the backend.
 * @param payload - The entity data to create.
 * @param token - Access token for authentication.
 * @returns A promise that resolves to the created entity.
 */
export const createEntity = async (
  payload: CreateEntityDto,
  token: string
): Promise<Entity> => {
  const response = await fetch(`${API_BASE_URL}/entities`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({
      message: 'Failed to create entity and parse error response'
    }));
    console.error('Error creating entity:', errorData);

    throw new Error(
      errorData.message || `Failed to create entity. Status: ${response.status}`,
    );
  }

  return response.json();
};

/**
 * Sends a chat message to the AI chat endpoint
 * @param payload - The chat request containing message and optional context
 * @param token - Optional access token for authenticated requests
 * @returns A promise that resolves to the AI's chat response
 */
export const postChatMessage = async (
  payload: ChatRequest,
  token?: string | null,
): Promise<ChatResponse> => {
  const response = await fetch(`${API_BASE_URL}/chat`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({
      message: 'Failed to send chat message and parse error response'
    }));
    console.error('Error sending chat message:', errorData);
    throw new Error(
      errorData.message || `Failed to send chat message. Status: ${response.status}`,
    );
  }

  return response.json();
};

/**
 * Creates a new chat message object with proper formatting
 * @param text - The message text
 * @param sender - Who sent the message ('user' or 'ai')
 * @param recommendedEntities - Optional entities to include with AI messages
 * @returns A properly formatted ChatMessage object
 */
export const createChatMessage = (
  text: string,
  sender: 'user' | 'ai',
  recommendedEntities?: Entity[],
): ChatMessage => {
  return {
    id: `${sender}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    text,
    sender,
    timestamp: new Date(),
    ...(recommendedEntities && { recommendedEntities }),
  };
};

// ===== CHAT HISTORY API FUNCTIONS (Future Backend Integration) =====

/**
 * Get user's chat conversations from backend (placeholder for future implementation)
 * @param token - Access token for authenticated requests
 * @param page - Optional page number for pagination
 * @param limit - Optional limit for pagination
 * @returns A promise that resolves to user's chat conversations
 */
export const getChatConversations = async (
  token: string,
  page?: number,
  limit?: number,
): Promise<{ data: any[]; meta: any }> => {
  // TODO: Implement when backend endpoints are available
  // const queryParams = new URLSearchParams();
  // if (page) queryParams.append('page', page.toString());
  // if (limit) queryParams.append('limit', limit.toString());

  // const response = await fetch(`${API_BASE_URL}/chat/conversations?${queryParams.toString()}`, {
  //   method: 'GET',
  //   headers: {
  //     'Authorization': `Bearer ${token}`,
  //   },
  // });

  // if (!response.ok) {
  //   throw new Error(`Failed to fetch chat conversations. Status: ${response.status}`);
  // }

  // return response.json();

  // For now, return empty data
  return { data: [], meta: { total: 0, page: 1, limit: 20, totalPages: 0 } };
};

/**
 * Get a specific chat conversation by ID from backend (placeholder for future implementation)
 * @param conversationId - The conversation ID
 * @param token - Access token for authenticated requests
 * @returns A promise that resolves to the conversation details
 */
export const getChatConversation = async (
  conversationId: string,
  token: string,
): Promise<any> => {
  // TODO: Implement when backend endpoints are available
  // const response = await fetch(`${API_BASE_URL}/chat/conversations/${conversationId}`, {
  //   method: 'GET',
  //   headers: {
  //     'Authorization': `Bearer ${token}`,
  //   },
  // });

  // if (!response.ok) {
  //   throw new Error(`Failed to fetch conversation. Status: ${response.status}`);
  // }

  // return response.json();

  throw new Error('Chat conversation API not yet implemented');
};

/**
 * Delete a chat conversation from backend (placeholder for future implementation)
 * @param conversationId - The conversation ID to delete
 * @param token - Access token for authenticated requests
 * @returns A promise that resolves when the conversation is deleted
 */
export const deleteChatConversation = async (
  conversationId: string,
  token: string,
): Promise<void> => {
  // TODO: Implement when backend endpoints are available
  // const response = await fetch(`${API_BASE_URL}/chat/conversations/${conversationId}`, {
  //   method: 'DELETE',
  //   headers: {
  //     'Authorization': `Bearer ${token}`,
  //   },
  // });

  // if (!response.ok) {
  //   throw new Error(`Failed to delete conversation. Status: ${response.status}`);
  // }

  throw new Error('Delete chat conversation API not yet implemented');
};

// ===== ADMIN API FUNCTIONS =====

/**
 * Admin: Get all users with pagination and filtering
 */
export const adminGetUsers = async (
  params: {
    page?: number;
    limit?: number;
    status?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  },
  token: string,
) => {
  const searchParams = new URLSearchParams();
  if (params.page) searchParams.append('page', params.page.toString());
  if (params.limit) searchParams.append('limit', params.limit.toString());
  if (params.status) searchParams.append('status', params.status);
  if (params.sortBy) searchParams.append('sortBy', params.sortBy);
  if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);

  const response = await fetch(`${API_BASE_URL}/admin/users?${searchParams}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch users. Status: ${response.status}`);
  }

  return response.json();
};

/**
 * Admin: Get user by ID
 */
export const adminGetUserById = async (userId: string, token: string) => {
  const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch user. Status: ${response.status}`);
  }

  return response.json();
};

/**
 * Admin: Update user status
 */
export const adminUpdateUserStatus = async (
  userId: string,
  status: string,
  token: string,
) => {
  const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/status`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({ status }),
  });

  if (!response.ok) {
    throw new Error(`Failed to update user status. Status: ${response.status}`);
  }

  return response.json();
};

/**
 * Admin: Update user role
 */
export const adminUpdateUserRole = async (
  userId: string,
  role: string,
  token: string,
) => {
  const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/role`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({ role }),
  });

  if (!response.ok) {
    throw new Error(`Failed to update user role. Status: ${response.status}`);
  }

  return response.json();
};

/**
 * Admin: Update entity status
 */
export const adminUpdateEntityStatus = async (
  entityId: string,
  status: string,
  token: string,
) => {
  const response = await fetch(`${API_BASE_URL}/admin/entities/${entityId}/status`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({ status }),
  });

  if (!response.ok) {
    throw new Error(`Failed to update entity status. Status: ${response.status}`);
  }

  return response.json();
};

/**
 * Admin: Get application settings
 */
export const adminGetSettings = async (token: string) => {
  const response = await fetch(`${API_BASE_URL}/admin/settings`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch settings. Status: ${response.status}`);
  }

  return response.json();
};

/**
 * Admin: Update application setting
 */
export const adminUpdateSetting = async (
  key: string,
  value: string,
  token: string,
) => {
  const response = await fetch(`${API_BASE_URL}/admin/settings/${key}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify({ value }),
  });

  if (!response.ok) {
    throw new Error(`Failed to update setting. Status: ${response.status}`);
  }

  return response.json();
};

/**
 * Get all tool requests (public endpoint)
 */
export const getToolRequests = async (
  params: {
    page?: number;
    limit?: number;
    status?: string;
    priority?: string;
  } = {},
  token?: string | null,
) => {
  const searchParams = new URLSearchParams();
  if (params.page) searchParams.append('page', params.page.toString());
  if (params.limit) searchParams.append('limit', params.limit.toString());
  if (params.status) searchParams.append('status', params.status);
  if (params.priority) searchParams.append('priority', params.priority);

  const response = await fetch(`${API_BASE_URL}/tool-requests?${searchParams}`, {
    headers: {
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch tool requests. Status: ${response.status}`);
  }

  return response.json();
};