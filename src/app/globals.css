@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Brand Colors - Modern Indigo-600 primary */
    --primary: 239 84% 67%; /* #4f46e5 - Indigo-600 for main CTAs */
    --primary-foreground: 0 0% 98%;
    --secondary: 45 93% 47%; /* #f59e0b - Amber for accents */
    --secondary-foreground: 0 0% 9%;
    --success: 142 71% 45%; /* #10b981 - Emerald for positive feedback */
    --success-foreground: 0 0% 98%;

    /* Neutral Scale - Improved contrast */
    --background: 0 0% 100%;
    --foreground: 220 13% 9%; /* #111827 - Gray-900 for primary text */
    --card: 0 0% 100%;
    --card-foreground: 220 13% 9%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 9%;

    /* Muted colors for secondary text */
    --muted: 220 13% 91%; /* #f3f4f6 - Gray-100 for backgrounds */
    --muted-foreground: 220 9% 46%; /* #6b7280 - Gray-500 for secondary text */

    /* Accent colors */
    --accent: 220 13% 91%;
    --accent-foreground: 220 13% 9%;

    /* Destructive */
    --destructive: 0 84% 60%; /* #ef4444 - Red for errors */
    --destructive-foreground: 0 0% 98%;

    /* Borders and inputs */
    --border: 220 13% 91%; /* #e5e7eb - Gray-200 for borders */
    --input: 220 13% 91%;
    --ring: 239 68% 68%; /* Match primary for focus rings */

    /* Chart colors */
    --chart-1: 239 68% 68%;
    --chart-2: 45 93% 47%;
    --chart-3: 142 71% 45%;
    --chart-4: 0 84% 60%;
    --chart-5: 262 83% 58%;

    --radius: 0.5rem;
  }

  .dark {
    /* Dark mode - Enhanced for better contrast */
    --background: 220 13% 9%; /* #111827 - Gray-900 */
    --foreground: 0 0% 98%;
    --card: 220 13% 11%; /* #1f2937 - Gray-800 */
    --card-foreground: 0 0% 98%;
    --popover: 220 13% 11%;
    --popover-foreground: 0 0% 98%;

    /* Primary stays bright in dark mode */
    --primary: 239 68% 68%;
    --primary-foreground: 220 13% 9%;

    /* Secondary adjusted for dark mode */
    --secondary: 45 93% 47%;
    --secondary-foreground: 220 13% 9%;

    /* Muted colors for dark mode */
    --muted: 220 13% 15%; /* #374151 - Gray-700 */
    --muted-foreground: 220 9% 64%; /* #9ca3af - Gray-400 */

    --accent: 220 13% 15%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 13% 15%;
    --input: 220 13% 15%;
    --ring: 239 68% 68%;

    /* Chart colors for dark mode */
    --chart-1: 239 68% 68%;
    --chart-2: 45 93% 47%;
    --chart-3: 142 71% 45%;
    --chart-4: 0 84% 60%;
    --chart-5: 262 83% 58%;
  }
}



@layer base {
  * {
    @apply border-border transition-colors;
  }
  body {
    @apply bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* Typography Scale */
  h1 {
    @apply text-3xl font-bold leading-tight tracking-tight;
  }
  h2 {
    @apply text-2xl font-semibold leading-tight;
  }
  h3 {
    @apply text-lg font-medium leading-snug;
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    @apply outline-none ring-2 ring-primary ring-offset-2 ring-offset-background;
  }
}

@layer components {
  /* Card hover effects with proper shadows */
  .card-hover {
    @apply transition-all duration-200 ease-in-out shadow-sm hover:shadow-xl hover:-translate-y-1;
  }

  /* Modern card styling */
  .modern-card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-xl transition-all duration-200;
  }

  /* Filter pill styles */
  .filter-pill {
    @apply inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium bg-primary/10 text-primary rounded-full border border-primary/20 transition-colors hover:bg-primary/20;
  }

  /* Interactive states */
  .interactive-hover {
    @apply transition-colors duration-150 hover:bg-accent hover:text-accent-foreground;
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  /* Chat animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeInUp {
    animation: fadeInUp 0.5s ease-out forwards;
  }

  /* Respect user's motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .animate-fadeInUp,
    .animate-bounce,
    .animate-spin,
    .animate-pulse {
      animation: none;
    }

    .transition-all,
    .transition-colors,
    .transition-transform,
    .transition-opacity {
      transition: none;
    }

    .hover\:scale-105:hover,
    .hover\:scale-110:hover,
    .hover\:-translate-y-1:hover,
    .group-hover\:scale-105,
    .group-hover\:scale-110,
    .group-hover\:translate-x-0\.5 {
      transform: none;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .bg-white\/80,
    .bg-white\/90,
    .bg-white\/95 {
      @apply bg-white;
    }

    .border-gray-200\/50,
    .border-gray-100\/50 {
      @apply border-gray-400;
    }

    .text-gray-500,
    .text-gray-600 {
      @apply text-gray-800;
    }

    .bg-gradient-to-br,
    .bg-gradient-to-r {
      @apply bg-indigo-600;
    }
  }

  /* Ensure sufficient color contrast */
  .text-muted-foreground {
    color: hsl(220 9% 40%); /* Darker than default for better contrast */
  }

  /* Improved checkbox styles */
  .checkbox-custom {
    @apply h-4 w-4 rounded border-2 border-muted-foreground/30 bg-background transition-colors focus:ring-2 focus:ring-primary focus:ring-offset-2 checked:bg-primary checked:border-primary;
  }
}

@layer utilities {
  /* Text truncation utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Spacing utilities following 8px grid */
  .space-y-8 > * + * {
    margin-top: 2rem;
  }

  /* Smooth scrolling */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  /* Backdrop blur for mobile overlays */
  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }
}
